import 'package:flutter/foundation.dart';

/// Model for Solution creation and management
class SolutionCreationModel {
  final String? id;
  final String name;
  final String? displayName;
  final String? description;
  final String? businessPurpose;
  final String? businessDomain;
  final String? category;
  final String? colorTheme;
  final String? icon;
  final List<String>? tags;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  
  // Solution-specific properties
  final LODetails? loDetails;
  final InputsStack? inputsStack;
  final OutputStack? outputStack;
  final ValidationStack? validationStack;
  final UIStack? uiStack;
  final MappingStack? mappingStack;
  final NestedFunctionPathways? nestedFunctionPathways;
  final ExecutionPathway? executionPathway;

  SolutionCreationModel({
    this.id,
    required this.name,
    this.displayName,
    this.description,
    this.businessPurpose,
    this.businessDomain,
    this.category,
    this.colorTheme,
    this.icon,
    this.tags,
    this.createdAt,
    this.updatedAt,
    this.loDetails,
    this.inputsStack,
    this.outputStack,
    this.validationStack,
    this.uiStack,
    this.mappingStack,
    this.nestedFunctionPathways,
    this.executionPathway,
  });

  factory SolutionCreationModel.fromJson(Map<String, dynamic> json) {
    return SolutionCreationModel(
      id: json['id']?.toString(),
      name: json['name']?.toString() ?? '',
      displayName: json['displayName']?.toString(),
      description: json['description']?.toString(),
      businessPurpose: json['businessPurpose']?.toString(),
      businessDomain: json['businessDomain']?.toString(),
      category: json['category']?.toString(),
      colorTheme: json['colorTheme']?.toString(),
      icon: json['icon']?.toString(),
      tags: json['tags'] != null ? List<String>.from(json['tags']) : null,
      createdAt: json['createdAt'] != null ? DateTime.parse(json['createdAt']) : null,
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
      loDetails: json['loDetails'] != null ? LODetails.fromJson(json['loDetails']) : null,
      inputsStack: json['inputsStack'] != null ? InputsStack.fromJson(json['inputsStack']) : null,
      outputStack: json['outputStack'] != null ? OutputStack.fromJson(json['outputStack']) : null,
      validationStack: json['validationStack'] != null ? ValidationStack.fromJson(json['validationStack']) : null,
      uiStack: json['uiStack'] != null ? UIStack.fromJson(json['uiStack']) : null,
      mappingStack: json['mappingStack'] != null ? MappingStack.fromJson(json['mappingStack']) : null,
      nestedFunctionPathways: json['nestedFunctionPathways'] != null ? NestedFunctionPathways.fromJson(json['nestedFunctionPathways']) : null,
      executionPathway: json['executionPathway'] != null ? ExecutionPathway.fromJson(json['executionPathway']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'displayName': displayName,
      'description': description,
      'businessPurpose': businessPurpose,
      'businessDomain': businessDomain,
      'category': category,
      'colorTheme': colorTheme,
      'icon': icon,
      'tags': tags,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'loDetails': loDetails?.toJson(),
      'inputsStack': inputsStack?.toJson(),
      'outputStack': outputStack?.toJson(),
      'validationStack': validationStack?.toJson(),
      'uiStack': uiStack?.toJson(),
      'mappingStack': mappingStack?.toJson(),
      'nestedFunctionPathways': nestedFunctionPathways?.toJson(),
      'executionPathway': executionPathway?.toJson(),
    };
  }

  SolutionCreationModel copyWith({
    String? id,
    String? name,
    String? displayName,
    String? description,
    String? businessPurpose,
    String? businessDomain,
    String? category,
    String? colorTheme,
    String? icon,
    List<String>? tags,
    DateTime? createdAt,
    DateTime? updatedAt,
    LODetails? loDetails,
    InputsStack? inputsStack,
    OutputStack? outputStack,
    ValidationStack? validationStack,
    UIStack? uiStack,
    MappingStack? mappingStack,
    NestedFunctionPathways? nestedFunctionPathways,
    ExecutionPathway? executionPathway,
  }) {
    return SolutionCreationModel(
      id: id ?? this.id,
      name: name ?? this.name,
      displayName: displayName ?? this.displayName,
      description: description ?? this.description,
      businessPurpose: businessPurpose ?? this.businessPurpose,
      businessDomain: businessDomain ?? this.businessDomain,
      category: category ?? this.category,
      colorTheme: colorTheme ?? this.colorTheme,
      icon: icon ?? this.icon,
      tags: tags ?? this.tags,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      loDetails: loDetails ?? this.loDetails,
      inputsStack: inputsStack ?? this.inputsStack,
      outputStack: outputStack ?? this.outputStack,
      validationStack: validationStack ?? this.validationStack,
      uiStack: uiStack ?? this.uiStack,
      mappingStack: mappingStack ?? this.mappingStack,
      nestedFunctionPathways: nestedFunctionPathways ?? this.nestedFunctionPathways,
      executionPathway: executionPathway ?? this.executionPathway,
    );
  }
}

/// LO Details model
class LODetails {
  final String? status;
  final int? count;
  final LeaveApplicationObjectConfiguration? leaveApplicationObjectConfiguration;

  LODetails({
    this.status,
    this.count,
    this.leaveApplicationObjectConfiguration,
  });

  factory LODetails.fromJson(Map<String, dynamic> json) {
    return LODetails(
      status: json['status']?.toString(),
      count: json['count'] as int?,
      leaveApplicationObjectConfiguration: json['leaveApplicationObjectConfiguration'] != null 
        ? LeaveApplicationObjectConfiguration.fromJson(json['leaveApplicationObjectConfiguration']) 
        : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'count': count,
      'leaveApplicationObjectConfiguration': leaveApplicationObjectConfiguration?.toJson(),
    };
  }
}

/// Leave Application Object Configuration model
class LeaveApplicationObjectConfiguration {
  final String? name;
  final String? displayName;
  final String? workflowOrigin;
  final String? pathwayType;
  final String? functionType;
  final String? agentType;
  final String? uiType;
  final String? roleName;

  LeaveApplicationObjectConfiguration({
    this.name,
    this.displayName,
    this.workflowOrigin,
    this.pathwayType,
    this.functionType,
    this.agentType,
    this.uiType,
    this.roleName,
  });

  factory LeaveApplicationObjectConfiguration.fromJson(Map<String, dynamic> json) {
    return LeaveApplicationObjectConfiguration(
      name: json['name']?.toString(),
      displayName: json['displayName']?.toString(),
      workflowOrigin: json['workflowOrigin']?.toString(),
      pathwayType: json['pathwayType']?.toString(),
      functionType: json['functionType']?.toString(),
      agentType: json['agentType']?.toString(),
      uiType: json['uiType']?.toString(),
      roleName: json['roleName']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'displayName': displayName,
      'workflowOrigin': workflowOrigin,
      'pathwayType': pathwayType,
      'functionType': functionType,
      'agentType': agentType,
      'uiType': uiType,
      'roleName': roleName,
    };
  }
}

/// Inputs Stack model
class InputsStack {
  final String? status;
  final int? attributesCount;

  InputsStack({
    this.status,
    this.attributesCount,
  });

  factory InputsStack.fromJson(Map<String, dynamic> json) {
    return InputsStack(
      status: json['status']?.toString(),
      attributesCount: json['attributesCount'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'attributesCount': attributesCount,
    };
  }
}

/// Output Stack model
class OutputStack {
  final String? status;
  final int? configuredCount;

  OutputStack({
    this.status,
    this.configuredCount,
  });

  factory OutputStack.fromJson(Map<String, dynamic> json) {
    return OutputStack(
      status: json['status']?.toString(),
      configuredCount: json['configuredCount'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'configuredCount': configuredCount,
    };
  }
}

/// Validation Stack model
class ValidationStack {
  final String? status;
  final int? rulesCount;

  ValidationStack({
    this.status,
    this.rulesCount,
  });

  factory ValidationStack.fromJson(Map<String, dynamic> json) {
    return ValidationStack(
      status: json['status']?.toString(),
      rulesCount: json['rulesCount'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'rulesCount': rulesCount,
    };
  }
}

/// UI Stack model
class UIStack {
  final String? status;
  final int? configuredCount;

  UIStack({
    this.status,
    this.configuredCount,
  });

  factory UIStack.fromJson(Map<String, dynamic> json) {
    return UIStack(
      status: json['status']?.toString(),
      configuredCount: json['configuredCount'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'configuredCount': configuredCount,
    };
  }
}

/// Mapping Stack model
class MappingStack {
  final String? status;
  final int? mappingCount;

  MappingStack({
    this.status,
    this.mappingCount,
  });

  factory MappingStack.fromJson(Map<String, dynamic> json) {
    return MappingStack(
      status: json['status']?.toString(),
      mappingCount: json['mappingCount'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'mappingCount': mappingCount,
    };
  }
}

/// Nested Function Pathways model
class NestedFunctionPathways {
  final String? status;
  final int? pathwaysCount;

  NestedFunctionPathways({
    this.status,
    this.pathwaysCount,
  });

  factory NestedFunctionPathways.fromJson(Map<String, dynamic> json) {
    return NestedFunctionPathways(
      status: json['status']?.toString(),
      pathwaysCount: json['pathwaysCount'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'pathwaysCount': pathwaysCount,
    };
  }
}

/// Execution Pathway model
class ExecutionPathway {
  final String? status;
  final int? routesCount;

  ExecutionPathway({
    this.status,
    this.routesCount,
  });

  factory ExecutionPathway.fromJson(Map<String, dynamic> json) {
    return ExecutionPathway(
      status: json['status']?.toString(),
      routesCount: json['routesCount'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'routesCount': routesCount,
    };
  }
}
