import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/providers/auth_provider.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/screens/new_design/my_library_mobile/create_flow_main_screen.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:nsl/widgets/responsive_home_new_builder.dart';
import 'package:provider/provider.dart';
import 'custom_drawer_item.dart';

class CustomDrawer extends StatelessWidget {
  const CustomDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: MediaQuery.of(context).size.width,
      child: Drawer(
        backgroundColor: Color(0xffF6F6F6),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.zero),
        child: Column(
          children: [
            // const SizedBox(height: 20),
            const SizedBox(height: 40),
            // Profile Header
            Container(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 20),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.zero,
              ),
              child: Row(
                children: [
                  SvgPicture.asset("assets/images/profile.svg"),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Consumer<AuthProvider>(
                      builder: (context, authProvider, _) {
                        final String profileName =
                            authProvider.user?.username ?? 'Nsl Admin';
                        return Text(
                          profileName,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w500,
                          ),
                        );
                      },
                    ),
                  ),
                  Container(
                    width: 30,
                    height: 30,
                    decoration: BoxDecoration(
                      color: Color(0xffE5EBFD),
                      shape: BoxShape.circle,
                    ),
                    child: IconButton(
                      padding: EdgeInsets.zero, // Remove default padding
                      icon: Icon(Icons.close, size: 16, color: Colors.black),
                      onPressed: () => Navigator.pop(context),
                      constraints:
                          BoxConstraints(), // Remove default constraints
                    ),
                  ),
                ],
              ),
            ),

            // Drawer Items
            Expanded(
              child: ListView(
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                children: [
                  CustomDrawerItem(
                    icon: 'assets/images/sidebar_icons/chat.svg',
                    title: 'Chat',
                    onTap: () {
                      final provider =
                          Provider.of<WebHomeProvider>(context, listen: false);
                      provider.resetConversation();
                      provider.selectedQuickMessage = 'NSL';
                      Navigator.popUntil(context, (route) => route.isFirst);
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => ResponsiveHomeNewBuilder(
                              routeName: ScreenConstants.home),
                        ),
                      );
                    },
                  ),
                  CustomDrawerItem(
                    icon: 'assets/images/sidebar_icons/create.svg',
                    title: 'Create',
                    onTap: null,
                    children: [
                      ListTile(
                          leading: SvgPicture.asset(
                            'assets/images/sidebar_icons/mylibrary.svg',
                            width: 32,
                            height: 32,
                          ),
                          title: const Text('My Library'),
                          horizontalTitleGap: 8,
                          onTap: () {
                            Navigator.pushAndRemoveUntil(
                              context,
                              PageRouteBuilder(
                                pageBuilder:
                                    (context, animation, secondaryAnimation) =>
                                        MobileCreationFlowMainScreen(),
                                transitionsBuilder: (context, animation,
                                    secondaryAnimation, child) {
                                  const begin = Offset(-1.0, 0.0);
                                  const end = Offset.zero;
                                  const curve = Curves.easeInOut;
                                  final tween = Tween(begin: begin, end: end)
                                      .chain(CurveTween(curve: curve));
                                  return SlideTransition(
                                    position: animation.drive(tween),
                                    child: child,
                                  );
                                },
                                transitionDuration:
                                    const Duration(milliseconds: 600),
                              ),
                              (route) => false, // Remove all previous routes
                            );
                          }),
                      ListTile(
                          leading: SvgPicture.asset(
                            'assets/images/sidebar_icons/organizer.svg',
                            width: 32,
                            height: 32,
                          ),
                          title: const Text('Organizer'),
                          horizontalTitleGap: 8,
                          onTap: () {
                            Navigator.popUntil(
                                context, (route) => route.isFirst);
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) =>
                                    MobileCreationFlowMainScreen(),
                              ),
                            );
                          }),
                      ListTile(
                          leading: SvgPicture.asset(
                            'assets/images/sidebar_icons/testing.svg',
                            width: 32,
                            height: 32,
                          ),
                          title: const Text('Testing'),
                          horizontalTitleGap: 8,
                          onTap: () {
                            Navigator.popUntil(
                                context, (route) => route.isFirst);
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) =>
                                    MobileCreationFlowMainScreen(),
                              ),
                            );
                          }),
                    ],
                  ),
                  CustomDrawerItem(
                    icon: 'assets/images/sidebar_icons/business.svg',
                    title: 'My Business',
                    onTap: null,
                    children: [
                      ListTile(
                        leading: SvgPicture.asset(
                          'assets/images/my_business/home_business.svg',
                          width: 20,
                          height: 20,
                        ),
                        title: const Text('Home'),
                        horizontalTitleGap: 8,
                        onTap: () {
                          Navigator.popUntil(context, (route) => route.isFirst);
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => ResponsiveHomeNewBuilder(
                                  routeName: ScreenConstants.myBusinessHome),
                            ),
                          );
                        },
                      ),
                      ListTile(
                          leading: SvgPicture.asset(
                            'assets/images/my_business/collection_business.svg',
                            width: 20,
                            height: 20,
                          ),
                          title: const Text('Collections'),
                          horizontalTitleGap: 8,
                          onTap: () {
                            Navigator.popUntil(
                                context, (route) => route.isFirst);
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => ResponsiveHomeNewBuilder(
                                    routeName:
                                        ScreenConstants.myBusinessCollections),
                              ),
                            );
                          }),
                      ListTile(
                          leading: SvgPicture.asset(
                            'assets/images/my_business/solutions_business.svg',
                            width: 20,
                            height: 20,
                          ),
                          title: const Text('Solutions'),
                          horizontalTitleGap: 8,
                          onTap: () {
                            Navigator.popUntil(
                                context, (route) => route.isFirst);
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => ResponsiveHomeNewBuilder(
                                    routeName:
                                        ScreenConstants.myBusinessSolutions),
                              ),
                            );
                          }),
                      ListTile(
                        leading: SvgPicture.asset(
                          'assets/images/my_business/records_business.svg',
                          width: 20,
                          height: 20,
                        ),
                        title: const Text('Records'),
                        horizontalTitleGap: 8,
                        onTap: () {
                          Navigator.popUntil(context, (route) => route.isFirst);
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => ResponsiveHomeNewBuilder(
                                  routeName: ScreenConstants.myBusinessRecords),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                  CustomDrawerItem(
                    icon: 'assets/images/sidebar_icons/my_transaction.svg',
                    title: 'MY Transaction',
                    onTap: () => Navigator.pop(context),
                  ),
                  CustomDrawerItem(
                    icon: 'assets/images/sidebar_icons/calendar.svg',
                    title: 'Calendar',
                    onTap: () => Navigator.pop(context),
                  ),
                  CustomDrawerItem(
                    icon: 'assets/images/sidebar_icons/notifications.svg',
                    title: 'Notification',
                    onTap: () => Navigator.pop(context),
                  ),
                  ListTile(
                    leading: SvgPicture.asset(
                      'assets/images/sidebar_icons/reconciliation.svg',
                      width: 32,
                      height: 32,
                    ),
                    title: const Text('Reconciliation'),
                    horizontalTitleGap: 8,
                    onTap: () => Navigator.pop(context),
                  ),
                  CustomDrawerItem(
                      icon: 'assets/images/sidebar_icons/instant-financial.svg',
                      title: 'Instant Financial',
                      onTap: () {
                        Navigator.popUntil(context, (route) => route.isFirst);
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => ResponsiveHomeNewBuilder(
                                routeName: ScreenConstants.treeModel),
                          ),
                        );
                      }),
                  CustomDrawerItem(
                      icon: 'assets/images/sidebar_icons/code.svg',
                      title: 'Demo',
                      onTap: () {
                        Navigator.popUntil(context, (route) => route.isFirst);
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => ResponsiveHomeNewBuilder(
                                routeName:
                                    ScreenConstants.webTransactionWidgetsDemo),
                          ),
                        );
                      }),
                  CustomDrawerItem(
                      icon: 'assets/images/sidebar_icons/code.svg',
                      title: 'Object Flow',
                      onTap: () {
                        Navigator.popUntil(context, (route) => route.isFirst);
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => ResponsiveHomeNewBuilder(
                                routeName:
                                    ScreenConstants.createObjectScreenMobile),
                          ),
                        );
                      }),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
