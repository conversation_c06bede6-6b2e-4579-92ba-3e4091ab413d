import 'package:flutter/material.dart';
import 'package:nsl/providers/auth_provider.dart';
import 'package:nsl/screens/new_design/home_screen_new.dart';
import 'package:nsl/screens/new_design/my_business/collection.dart';
import 'package:nsl/screens/new_design/my_business/home.dart';
import 'package:nsl/screens/new_design/my_business/solution.dart';
import 'package:nsl/screens/new_design/my_library_mobile/create_book_mobile_new.dart';
import 'package:nsl/screens/new_design/nsl_hierarchy_mobile/nsl_hierarchy_mobile_screen.dart';
import 'package:nsl/screens/new_design/object_flow_static_mobile/create_object_mobile.dart';
import 'package:nsl/screens/new_design/object_flow_static_mobile/extract_details_object_mobile.dart';
import 'package:nsl/screens/web/new_design/web_home_screen_new.dart';
import 'package:nsl/screens/web_transaction/web_transaction_widgets_demo.dart';
import 'package:nsl/utils/navigation_service.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:provider/provider.dart';
import '../providers/responsive_state_provider.dart';
import '../providers/web_home_provider.dart';
import '../utils/logger.dart';
import 'base_responsive_builder.dart';

class ResponsiveHomeNewBuilder extends BaseResponsiveBuilder {
  const ResponsiveHomeNewBuilder({super.key, this.routeName})
      : super(builderKey: 'home');
  final String? routeName;

  @override
  BaseResponsiveBuilderState<BaseResponsiveBuilder> createState() =>
      _ResponsiveHomeNewBuilderState();

  @override
  Widget buildWebLayout(BuildContext context, String? currentRoute) {
    // final isLoggedIn = Provider.of<AuthProvider>(context).isLoggedIn;
    final isLoggedIn = true;

    if (!isLoggedIn) {
      Future.microtask(() {
        NavigationService.navigateToReplacement('/login');
      });

      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    if (currentRoute != null) {
      try {
        final webHomeProvider =
            Provider.of<WebHomeProvider>(context, listen: false);
        webHomeProvider.currentScreenIndex = currentRoute;
        Logger.info('Set WebHomeProvider currentScreenIndex to $currentRoute');
      } catch (e) {
        Logger.error('Error setting WebHomeProvider currentScreenIndex: $e');
      }
    }

    return WebHomeScreenNew();
  }

  @override
  Widget buildMobileLayout(BuildContext context, String? currentRoute) {
    // Pass the current route to the mobile layout
    switch (routeName) {
      case ScreenConstants.home:
        return HomeScreenNew();
      // case ScreenConstants.nslRag:
      //    return WebHomeScreenChatNew();
      case ScreenConstants.create:
        //return WebMyLibraryScreen();
        return CreateBookMobileNew();
      //  case ScreenConstants.myLibrary:
      //     return WebMyLibraryScreen();
      // case ScreenConstants.nslJava:
      //   return NslJavaConverter();
      // return PreviewScreen();

      // case ScreenConstants.webMyLibrary:
      //   return WebMyLibraryScreen();

      // case ScreenConstants.webMyProjects:
      //   return WebMyProjectsScreen();

      // case ScreenConstants.webBookDetailPage:
      //   return BookDetailPage();

      // case ScreenConstants.webMySolution:
      //   return WebSolutionsScreen();

      // case ScreenConstants.webInsideBookModule:
      //   return WebInsideBookModule();

      // case ScreenConstants.webAddModulesPage:
      //   return WebAddModulesPage();

      // case ScreenConstants.solutionModulesPage:
      //   return SolutionModulesPage();

      // case ScreenConstants.webBookSolution:
      //   return WebBookSolutionPage();

      // case ScreenConstants.webMyObject:
      //   return WebObjectScreen();
      // case ScreenConstants.aiGeneratedObject:
      //   return AIGeneratedObjectScreen();
      // case ScreenConstants.manualGenerationSolution:
      //   return ManualCreationScreen();
      case ScreenConstants.myBusinessHome:
        return MyBusinessHome();
      case ScreenConstants.myBusinessCollections:
        return MyBusinessCollection(
          screenType: "collections",
        );
      case ScreenConstants.myBusinessSolutions:
        return MyBusinessSolution();
      case ScreenConstants.myBusinessRecords:
        return MyBusinessCollection(
          screenType: "records",
        );
      case ScreenConstants.webTransactionWidgetsDemo:
        return WebTransactionWidgetsDemo();
      // case ScreenConstants.myBusinessCollectionsModule:
      //   return WebCollectionWidgets();
      // case ScreenConstants.webSolutionWidgets:
      //   return WebSolutionWidgets();
      // case ScreenConstants.tempWebChat:
      //   return TempWebChat();
      // case ScreenConstants.webCollectionModuleDemo:
      //   return WebCollectionModuleDemo();
      // case ScreenConstants.webAgentScreen:
      //   return WebAgentScreen();
      // case ScreenConstants.dashboardWidgets:
      //   return DashboardWidgets();

      case ScreenConstants.treeModel:
        return NSLHierarchyMobileScreen();
      // case ScreenConstants.treeModel1:
      //   return TreeHierarchyModel1();
      // case ScreenConstants.discovery:
      //   return DiscoveryChatScreen();

      // case ScreenConstants.webHomeStatic:
      //   return WebHomeScreenChatStatic();

      // case ScreenConstants.manualCreationStaticScreen:
      //   return ManualCreationStaticScreen();
      case ScreenConstants.createObjectScreenMobile:
        return CreateObjectScreenMobile();
      case ScreenConstants.extractDetailsObjectMobile:
        return ExtractDetailsObjectMobile();
      default:
        /* return Container(
          alignment: Alignment.center,
          child: Text('Coming Soon111',
              style: TextStyle(
                fontSize: 24,
                fontFamily: 'TiemposText',
                fontWeight: FontWeight.bold,
              )),
        ); */

        return HomeScreenNew();
    }

    //  HomeScreenNewWithProvider(
    //   initialScreenIndex: currentRoute,
    // );
  }
}

class _ResponsiveHomeNewBuilderState
    extends BaseResponsiveBuilderState<ResponsiveHomeNewBuilder> {
  late WebHomeProvider _webHomeProvider;
  @override
  void initState() {
    super.initState();

    // Set up a listener for the WebHomeProvider's currentScreenIndex
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        final webHomeProvider =
            Provider.of<WebHomeProvider>(context, listen: false);

        // Listen for changes to the currentScreenIndex
        webHomeProvider.addListener(_handleScreenIndexChange);
      }
    });
  }

  @override
  void dispose() {
    // Use cached reference—no context access!
    try {
      _webHomeProvider.removeListener(_handleScreenIndexChange);
    } catch (e) {
      Logger.error('Error removing listener: $e');
    }
    super.dispose();
  }

  // Handle changes to the currentScreenIndex
  void _handleScreenIndexChange() {
    if (!mounted) return;

    try {
      final webHomeProvider =
          Provider.of<WebHomeProvider>(context, listen: false);
      final currentIndex = webHomeProvider.currentScreenIndex;

      // Store both the route and content state
      // This ensures both navigation and content changes are preserved
      storeContentState(currentIndex);

      Logger.info(
          'Content changed to: $currentIndex - Storing in state provider');
    } catch (e) {
      Logger.error('Error handling screen index change: $e');
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Defer state updates to avoid calling notifyListeners during build cycle
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;

      // Store the current screen index when the dependencies change
      try {
        final stateProvider =
            Provider.of<ResponsiveStateProvider>(context, listen: false);
// Only assign once to avoid multiple listeners.
        _webHomeProvider = Provider.of<WebHomeProvider>(context, listen: false);
        _webHomeProvider.addListener(_handleScreenIndexChange);
        // Store both the route (for navigation) and content state (for single-page changes)
        final currentIndex = _webHomeProvider.currentScreenIndex;
        stateProvider.setCurrentRoute(widget.builderKey, currentIndex);
        stateProvider.setContentState(widget.builderKey, currentIndex);

        Logger.info(
            'Dependencies changed - Storing screen index: $currentIndex');
      } catch (e) {
        Logger.error('Error storing current screen index: $e');
      }
    });
  }
}
